<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.9 Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36" version="27.0.9" pages="3">
  <diagram id="yW0WuKSwd8cGz2XZC77P" name="Page-3">
    <mxGraphModel dx="1186" dy="829" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="b4l5MEszTecYNtaIMKtk" name="Main - Create a campaign">
    <mxGraphModel dx="1186" dy="829" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="dUzS9tFZ41CwCp-0yK-n-1" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="360" height="360" as="geometry" />
        </mxCell>
        <mxCell id="dUzS9tFZ41CwCp-0yK-n-2" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="dUzS9tFZ41CwCp-0yK-n-4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="40" y="60" as="sourcePoint" />
            <mxPoint x="400" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dUzS9tFZ41CwCp-0yK-n-3" value="Configs" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="220" y="50" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="dUzS9tFZ41CwCp-0yK-n-4" value="Main" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#E6E6E6;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="150" y="50" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="dUzS9tFZ41CwCp-0yK-n-5" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0.994;entryY=0.053;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryPerimeter=0;" edge="1" parent="1" source="dUzS9tFZ41CwCp-0yK-n-3" target="dUzS9tFZ41CwCp-0yK-n-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="90" as="sourcePoint" />
            <mxPoint x="330" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="dUzS9tFZ41CwCp-0yK-n-20" value="&amp;nbsp; Please brief me the campaign requirements..." style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="70" y="110" width="300" height="200" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=-0.003;entryY=0.356;entryDx=0;entryDy=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;curved=1;entryPerimeter=0;" edge="1" parent="1" source="dUzS9tFZ41CwCp-0yK-n-21" target="Osi1AqXE5qDCXtsLcyBQ-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="dUzS9tFZ41CwCp-0yK-n-21" value="Analyse campaign brief" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="140" y="320" width="230" height="20" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-1" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="600" y="40" width="360" height="460" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-2" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="Osi1AqXE5qDCXtsLcyBQ-4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="600" y="60" as="sourcePoint" />
            <mxPoint x="960" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-3" value="Configs" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="780" y="50" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-4" value="Main" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#E6E6E6;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="710" y="50" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-5" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0.994;entryY=0.053;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryPerimeter=0;" edge="1" parent="1" source="Osi1AqXE5qDCXtsLcyBQ-3" target="Osi1AqXE5qDCXtsLcyBQ-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="780" y="90" as="sourcePoint" />
            <mxPoint x="890" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-6" value="&amp;nbsp; Carbon comb" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="740" y="110" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-7" value="Next" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="790" y="400" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-9" value="Back" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="70" y="320" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-11" value="&amp;nbsp; Brand awareness, Lead genera...." style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="740" y="150" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-12" value="&lt;div&gt;&amp;nbsp; 22-35&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="760" y="220" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-13" value="Campaign name" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="630" y="110" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-14" value="Campaign goals" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="630" y="150" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-15" value="Target audiences" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="630" y="190" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-16" value="Age range" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="650" y="220" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-17" value="&lt;div&gt;&amp;nbsp; all genders&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="760" y="260" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-18" value="Gender" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="650" y="260" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-21" value="&lt;div&gt;&amp;nbsp; Middle and high level&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="760" y="300" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-22" value="Income" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="650" y="300" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-26" value="&amp;nbsp; all levels" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="760" y="340" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-27" value="Educations" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="650" y="340" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Osi1AqXE5qDCXtsLcyBQ-28" value="Back" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="630" y="400" width="140" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="Settings" id="zkUl6zjAg-eR2dIxl-Kb">
    <mxGraphModel dx="1186" dy="829" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-1" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="360" height="360" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-4" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="vX-J96E7vZox5fPZ7a0Q-3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="40" y="60" as="sourcePoint" />
            <mxPoint x="400" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-2" value="Configs" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#E6E6E6;" vertex="1" parent="1">
          <mxGeometry x="220" y="50" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-3" value="Main" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="150" y="50" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-5" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0.994;entryY=0.053;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryPerimeter=0;" edge="1" parent="1" source="vX-J96E7vZox5fPZ7a0Q-2" target="vX-J96E7vZox5fPZ7a0Q-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="90" as="sourcePoint" />
            <mxPoint x="330" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-6" value="Provider:&amp;nbsp;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="90" y="90" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-11" value="Models:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="90" y="130" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-12" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="150" y="95" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-7" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-12">
          <mxGeometry width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-9" value="" style="endArrow=none;html=1;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;entryX=0.895;entryY=0.15;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.89;exitY=1.05;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="vX-J96E7vZox5fPZ7a0Q-12" source="vX-J96E7vZox5fPZ7a0Q-7" target="vX-J96E7vZox5fPZ7a0Q-7">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="195" as="sourcePoint" />
            <mxPoint x="110" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-10" value="" style="triangle;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;rotation=90;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-12">
          <mxGeometry x="195" y="5" width="10" height="10" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-13" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="150" y="136" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-14" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-13">
          <mxGeometry width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-15" value="" style="endArrow=none;html=1;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;entryX=0.895;entryY=0.15;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.89;exitY=1.05;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="vX-J96E7vZox5fPZ7a0Q-13" source="vX-J96E7vZox5fPZ7a0Q-14" target="vX-J96E7vZox5fPZ7a0Q-14">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="195" as="sourcePoint" />
            <mxPoint x="110" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-16" value="" style="triangle;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;rotation=90;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-13">
          <mxGeometry x="195" y="5" width="10" height="10" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;curved=1;dashed=1;" edge="1" parent="1" source="vX-J96E7vZox5fPZ7a0Q-17" target="vX-J96E7vZox5fPZ7a0Q-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-17" value="Load from hardcoded list" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;strokeColor=default;dashed=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="560" y="10" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;curved=1;dashed=1;" edge="1" parent="1" source="vX-J96E7vZox5fPZ7a0Q-19" target="vX-J96E7vZox5fPZ7a0Q-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-19" value="Load from hardcoded list&lt;div&gt;based on providers&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;strokeColor=default;dashed=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="560" y="185" width="100" height="70" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-21" value="API Key:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="90" y="170" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-22" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="150" y="176" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-23" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="840" width="360" height="360" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-24" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="vX-J96E7vZox5fPZ7a0Q-26">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="40" y="860" as="sourcePoint" />
            <mxPoint x="400" y="860" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-25" value="Configs" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#E6E6E6;" vertex="1" parent="1">
          <mxGeometry x="220" y="850" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-26" value="Main" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="150" y="850" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-27" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0.994;entryY=0.053;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryPerimeter=0;" edge="1" parent="1" source="vX-J96E7vZox5fPZ7a0Q-25" target="vX-J96E7vZox5fPZ7a0Q-23">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="890" as="sourcePoint" />
            <mxPoint x="330" y="890" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-28" value="Provider:&amp;nbsp;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="60" y="890" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-29" value="Models:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="60" y="930" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-30" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="167" y="895" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-31" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-30">
          <mxGeometry width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-32" value="" style="endArrow=none;html=1;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;entryX=0.895;entryY=0.15;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.89;exitY=1.05;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="vX-J96E7vZox5fPZ7a0Q-30" source="vX-J96E7vZox5fPZ7a0Q-31" target="vX-J96E7vZox5fPZ7a0Q-31">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="195" as="sourcePoint" />
            <mxPoint x="110" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-33" value="" style="triangle;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;rotation=90;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-30">
          <mxGeometry x="195" y="5" width="10" height="10" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-34" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="167" y="936" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-35" value="&amp;nbsp;Azure" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-34">
          <mxGeometry width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-36" value="" style="endArrow=none;html=1;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;entryX=0.895;entryY=0.15;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.89;exitY=1.05;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="vX-J96E7vZox5fPZ7a0Q-34" source="vX-J96E7vZox5fPZ7a0Q-35" target="vX-J96E7vZox5fPZ7a0Q-35">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="195" as="sourcePoint" />
            <mxPoint x="110" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-37" value="" style="triangle;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;rotation=90;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-34">
          <mxGeometry x="195" y="5" width="10" height="10" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-42" value="Azure endpoint:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="60" y="970" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-43" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="167" y="976" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-44" value="Azure credentials:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="60" y="1010" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-45" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="167" y="1016" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-46" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="520" y="840" width="360" height="360" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-47" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="vX-J96E7vZox5fPZ7a0Q-49">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="860" as="sourcePoint" />
            <mxPoint x="880" y="860" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-48" value="Configs" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#E6E6E6;" vertex="1" parent="1">
          <mxGeometry x="700" y="850" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-49" value="Main" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="630" y="850" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-50" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0.994;entryY=0.053;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryPerimeter=0;" edge="1" parent="1" source="vX-J96E7vZox5fPZ7a0Q-48" target="vX-J96E7vZox5fPZ7a0Q-46">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="890" as="sourcePoint" />
            <mxPoint x="810" y="890" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-51" value="Provider:&amp;nbsp;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="540" y="890" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-52" value="Models:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="540" y="930" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-53" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="647" y="895" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-54" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-53">
          <mxGeometry width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-55" value="" style="endArrow=none;html=1;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;entryX=0.895;entryY=0.15;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.89;exitY=1.05;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="vX-J96E7vZox5fPZ7a0Q-53" source="vX-J96E7vZox5fPZ7a0Q-54" target="vX-J96E7vZox5fPZ7a0Q-54">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="195" as="sourcePoint" />
            <mxPoint x="110" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-56" value="" style="triangle;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;rotation=90;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-53">
          <mxGeometry x="195" y="5" width="10" height="10" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-57" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="647" y="936" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-58" value="&amp;nbsp; AWS Bedrocks" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-57">
          <mxGeometry width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-59" value="" style="endArrow=none;html=1;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;entryX=0.895;entryY=0.15;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.89;exitY=1.05;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="vX-J96E7vZox5fPZ7a0Q-57" source="vX-J96E7vZox5fPZ7a0Q-58" target="vX-J96E7vZox5fPZ7a0Q-58">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="195" as="sourcePoint" />
            <mxPoint x="110" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-60" value="" style="triangle;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;rotation=90;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-57">
          <mxGeometry x="195" y="5" width="10" height="10" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-61" value="AWS Access Key ID" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="540" y="970" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-62" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="647" y="976" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-63" value="AWS Secret Access key" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="540" y="1010" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-64" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="647" y="1016" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-65" value="AWS Session Token" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="540" y="1050" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-66" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="647" y="1056" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-67" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="440" width="360" height="360" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-68" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="vX-J96E7vZox5fPZ7a0Q-70">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="40" y="460" as="sourcePoint" />
            <mxPoint x="400" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-69" value="Configs" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;fillColor=#E6E6E6;" vertex="1" parent="1">
          <mxGeometry x="220" y="450" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-70" value="Main" style="rounded=1;whiteSpace=wrap;html=1;sketch=1;curveFitting=1;jiggle=2;" vertex="1" parent="1">
          <mxGeometry x="150" y="450" width="70" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-71" value="" style="endArrow=none;html=1;rounded=0;sketch=1;curveFitting=1;jiggle=2;entryX=0.994;entryY=0.053;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryPerimeter=0;" edge="1" parent="1" source="vX-J96E7vZox5fPZ7a0Q-69" target="vX-J96E7vZox5fPZ7a0Q-67">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="490" as="sourcePoint" />
            <mxPoint x="330" y="490" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-72" value="Provider:&amp;nbsp;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="60" y="490" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-73" value="Models:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="60" y="530" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-74" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="167" y="495" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-75" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-74">
          <mxGeometry width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-76" value="" style="endArrow=none;html=1;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;entryX=0.895;entryY=0.15;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.89;exitY=1.05;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="vX-J96E7vZox5fPZ7a0Q-74" source="vX-J96E7vZox5fPZ7a0Q-75" target="vX-J96E7vZox5fPZ7a0Q-75">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="195" as="sourcePoint" />
            <mxPoint x="110" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-77" value="" style="triangle;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;rotation=90;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-74">
          <mxGeometry x="195" y="5" width="10" height="10" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-78" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="167" y="536" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-79" value="&amp;nbsp;OpenAI Like" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;align=left;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-78">
          <mxGeometry width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-80" value="" style="endArrow=none;html=1;rounded=0;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;entryX=0.895;entryY=0.15;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.89;exitY=1.05;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="vX-J96E7vZox5fPZ7a0Q-78" source="vX-J96E7vZox5fPZ7a0Q-79" target="vX-J96E7vZox5fPZ7a0Q-79">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="195" as="sourcePoint" />
            <mxPoint x="110" y="145" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-81" value="" style="triangle;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;rotation=90;" vertex="1" parent="vX-J96E7vZox5fPZ7a0Q-78">
          <mxGeometry x="195" y="5" width="10" height="10" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-82" value="Base URL:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="60" y="570" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-83" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="167" y="576" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-84" value="API Key:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="60" y="610" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="vX-J96E7vZox5fPZ7a0Q-85" value="" style="rounded=0;whiteSpace=wrap;html=1;sketch=1;hachureGap=4;jiggle=2;curveFitting=1;fontFamily=Architects Daughter;fontSource=https%3A%2F%2Ffonts.googleapis.com%2Fcss%3Ffamily%3DArchitects%2BDaughter;" vertex="1" parent="1">
          <mxGeometry x="167" y="616" width="210" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
