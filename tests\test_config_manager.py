import pytest
from pathlib import Path
from managers.config_manager import Confi<PERSON><PERSON><PERSON><PERSON>, Config, AIProvider
from platformdirs import user_config_dir

APP_NAME = "SoloAgency"
CONFIG_FILE = "config.json"


@pytest.fixture
def config_manager(tmp_path, monkeypatch):
    """Fixture to create a ConfigManager instance with a temporary config directory."""
    # Mock the user_config_dir to use our temporary directory
    monkeypatch.setattr(
        "managers.config_manager.user_config_dir", lambda app_name: str(tmp_path)
    )
    return ConfigManager()


def test_initialization(config_manager):
    """Test that ConfigManager initializes with default values."""
    config = config_manager.get_configs()
    assert isinstance(config, Config)
    assert config.ai_provider == AIProvider.NONE
    assert config.openai_api_key == ""
    assert config.anthropic_api_key == ""
    assert config.gemini_api_key == ""
    assert config.claude_api_key == ""
    assert config.groq_api_key == ""


def test_save_and_load_configs(config_manager):
    """Test saving and loading configs from file."""
    # Update some config values
    config_manager._config_cache.ai_provider = AIProvider.OPENAI
    config_manager._config_cache.openai_api_key = "test_key"
    config_manager._save_configs()

    # Create a new instance to test loading
    new_manager = ConfigManager()
    loaded_config = new_manager.get_configs()

    assert loaded_config.ai_provider == AIProvider.OPENAI
    assert loaded_config.openai_api_key == "test_key"


def test_update_openai_api_key(config_manager):
    """Test updating OpenAI API key."""
    test_key = "new_test_key"
    config_manager.update_openai_api_key(test_key)

    config = config_manager.get_configs()
    assert config.openai_api_key == test_key


def test_update_openai_api_key_empty(config_manager):
    """Test that updating with empty API key raises ValueError."""
    with pytest.raises(ValueError, match="API key cannot be empty"):
        config_manager.update_openai_api_key("")


def test_load_invalid_json(config_manager, tmp_path):
    """Test loading invalid JSON from config file."""
    config_path = Path(user_config_dir(APP_NAME)) / CONFIG_FILE
    config_path.parent.mkdir(parents=True, exist_ok=True)
    with open(config_path, "w") as f:
        f.write("invalid json")

    # Should not raise exception but reset to default config
    config_manager._load_configs()
    config = config_manager.get_configs()
    assert config.ai_provider == AIProvider.NONE
    assert config.openai_api_key == ""


def test_config_serialization():
    """Test Config serialization and deserialization."""
    config = Config(ai_provider=AIProvider.OPENAI, openai_api_key="test_key")

    # Test serialization
    config_dict = config.__dict__
    assert config_dict["ai_provider"] == AIProvider.OPENAI
    assert config_dict["openai_api_key"] == "test_key"

    # Test deserialization
    new_config = Config(**config_dict)
    assert new_config.ai_provider == AIProvider.OPENAI
    assert new_config.openai_api_key == "test_key"
