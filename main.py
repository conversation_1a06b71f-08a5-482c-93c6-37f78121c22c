from toga import App
from managers.config_manager import ConfigManager, APP_NAME
from managers.ui_manager import build_ui


def button_handler(widget):
    config_manager = ConfigManager()
    config = config_manager.get_configs()
    print(f"Current API Key: {config.ai_provider}")


def build(app):
    return build_ui()


def main():
    config_manager = ConfigManager()
    config = config_manager.get_configs()
    print(f"Current API Key: {config.ai_provider}")
    return App(APP_NAME, "hollytop.top.soloagency", startup=build)


if __name__ == "__main__":
    main().main_loop()
