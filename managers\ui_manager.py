import toga
from .config_manager import Config<PERSON><PERSON><PERSON>


def create_main_box():
    """Creates the main box containing the application title."""
    main_box = toga.Box()

    label = toga.Label("SoloAgency")
    label.style.margin = 20
    main_box.add(label)

    return main_box


def create_config_box():
    """Creates the configuration box containing the config check button."""
    config_box = toga.Box()

    button = toga.Button("Check Config", on_press=button_handler)
    button.style.margin = 50
    button.style.flex = 1
    config_box.add(button)

    return config_box


def button_handler(widget):
    """Handler for the config check button."""
    config_manager = ConfigManager()
    config = config_manager.get_configs()
    print(config)


def build_ui():
    """Builds the complete UI structure of the application."""
    main_box = create_main_box()
    config_box = create_config_box()

    container = toga.OptionContainer(
        content=[("Main", main_box), ("Configs", config_box)]
    )

    return container
